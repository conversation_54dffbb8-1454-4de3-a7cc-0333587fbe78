import DataTable from "@/components/tables/data-table";
import { userColumns, type User } from "@/components/tables/user-column";
import UserFilter from "@/components/UserFilter";

const data: User[] = [
  {
    id: 1,
    email: "<EMAIL>",
    username: "<PERSON>",
    gender: "Female",
    ip_address: "*************",
  },
  {
    id: 2,
    email: "<EMAIL>",
    username: "<PERSON>",
    gender: "Female",
    ip_address: "*************",
  },
  {
    id: 3,
    email: "<EMAIL>",
    username: "<PERSON>",
    gender: "Female",
    ip_address: "***************",
  },
  {
    id: 4,
    email: "wmar<PERSON><EMAIL>",
    username: "<PERSON><PERSON>",
    gender: "Male",
    ip_address: "************",
  },
  {
    id: 5,
    email: "<EMAIL>",
    username: "<PERSON>",
    gender: "Female",
    ip_address: "***************",
  },
  {
    id: 6,
    email: "d<PERSON><EMAIL>",
    username: "<PERSON><PERSON>",
    gender: "Female",
    ip_address: "*************",
  },
  {
    id: 7,
    email: "<EMAIL>",
    username: "Lancelot",
    gender: "Female",
    ip_address: "***************",
  },
  {
    id: 8,
    email: "<EMAIL>",
    username: "Rodolph",
    gender: "Female",
    ip_address: "***************",
  },
  {
    id: 9,
    email: "<EMAIL>",
    username: "Terrance",
    gender: "Female",
    ip_address: "************",
  },
  {
    id: 10,
    email: "<EMAIL>",
    username: "Vittorio",
    gender: "Male",
    ip_address: "*************",
  },
  {
    id: 11,
    email: "<EMAIL>",
    username: "Friedrick",
    gender: "Female",
    ip_address: "**************",
  },
  {
    id: 12,
    email: "<EMAIL>",
    username: "Redd",
    gender: "Male",
    ip_address: "************",
  },
  {
    id: 13,
    email: "<EMAIL>",
    username: "Niel",
    gender: "Male",
    ip_address: "************",
  },
  {
    id: 14,
    email: "<EMAIL>",
    username: "Domingo",
    gender: "Male",
    ip_address: "*************",
  },
  {
    id: 15,
    email: "<EMAIL>",
    username: "Benedick",
    gender: "Female",
    ip_address: "**************",
  },
  {
    id: 16,
    email: "<EMAIL>",
    username: "Jackson",
    gender: "Female",
    ip_address: "***********",
  },
  {
    id: 17,
    email: "<EMAIL>",
    username: "Baxy",
    gender: "Female",
    ip_address: "************",
  },
  {
    id: 18,
    email: "<EMAIL>",
    username: "Eric",
    gender: "Female",
    ip_address: "***********",
  },
  {
    id: 19,
    email: "<EMAIL>",
    username: "Bronson",
    gender: "Female",
    ip_address: "************",
  },
  {
    id: 20,
    email: "<EMAIL>",
    username: "Basilio",
    gender: "Male",
    ip_address: "***************",
  },
];

export default async function User() {
  return (
    <div>
      <UserFilter />
      <DataTable data={data} columns={userColumns} />
    </div>
  );
}
