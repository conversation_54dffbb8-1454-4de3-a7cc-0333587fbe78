"use client";

import { useState } from "react";
import TableFilter from "./TableFilter";
import { VisibilityState } from "@tanstack/react-table";

interface UserFilterProps {
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  columnVisibility?: VisibilityState;
  onColumnVisibilityChange?: (visibility: VisibilityState) => void;
}

const userColumns = [
  { id: "id", label: "ID" },
  { id: "email", label: "Email" },
  { id: "username", label: "Username" },
  { id: "gender", label: "Gender" },
  { id: "ip_address", label: "IP Address" },
];

export default function UserFilter({
  searchValue = "",
  onSearchChange,
  columnVisibility = {},
  onColumnVisibilityChange,
}: UserFilterProps) {
  const [internalSearchValue, setInternalSearchValue] = useState(searchValue);
  const [internalColumnVisibility, setInternalColumnVisibility] = useState<VisibilityState>(columnVisibility);

  const currentSearchValue = onSearchChange ? searchValue : internalSearchValue;
  const currentColumnVisibility = onColumnVisibilityChange ? columnVisibility : internalColumnVisibility;

  const handleSearchChange = (value: string) => {
    if (onSearchChange) {
      onSearchChange(value);
    } else {
      setInternalSearchValue(value);
    }
  };

  const handleColumnVisibilityChange = (visibility: VisibilityState) => {
    if (onColumnVisibilityChange) {
      onColumnVisibilityChange(visibility);
    } else {
      setInternalColumnVisibility(visibility);
    }
  };

  return (
    <TableFilter
      searchValue={currentSearchValue}
      onSearchChange={handleSearchChange}
      columnVisibility={currentColumnVisibility}
      onColumnVisibilityChange={handleColumnVisibilityChange}
      availableColumns={userColumns}
    />
  );
}
