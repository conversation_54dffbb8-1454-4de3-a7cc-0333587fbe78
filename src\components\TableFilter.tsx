"use client";

import { Input } from "./ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { VisibilityState } from "@tanstack/react-table";

interface TableFilterProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  columnVisibility: VisibilityState;
  onColumnVisibilityChange: (visibility: VisibilityState) => void;
  availableColumns: Array<{
    id: string;
    label: string;
  }>;
}

export default function TableFilter({
  searchValue,
  onSearchChange,
  columnVisibility,
  onColumnVisibilityChange,
  availableColumns,
}: TableFilterProps) {
  const handleColumnToggle = (columnId: string, checked: boolean) => {
    onColumnVisibilityChange({
      ...columnVisibility,
      [columnId]: checked,
    });
  };

  return (
    <div className="flex gap-2 mb-4">
      <Input
        placeholder="Search..."
        value={searchValue}
        onChange={(e) => onSearchChange(e.target.value)}
        className="flex-1"
      />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline">
            Toggle Columns
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {availableColumns.map((column) => (
            <DropdownMenuCheckboxItem
              key={column.id}
              className="capitalize"
              checked={columnVisibility[column.id] !== false}
              onCheckedChange={(checked) =>
                handleColumnToggle(column.id, !!checked)
              }
            >
              {column.label}
            </DropdownMenuCheckboxItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
