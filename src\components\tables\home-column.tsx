"use client";

import { createColumnHelper } from "@tanstack/react-table";

export type User = {
  firstName: string;
  lastName: string;
  age: number;
  visits: number;
  progress: number;
  status: string;
};

const columnHelper = createColumnHelper<User>();

export const homeColumns = [
  //   columnHelper.display({
  //     id: "actions",
  //     cell: (props) => <div>Hello</div>,
  //   }),
  columnHelper.group({
    header: "Info",
    footer: (props) => props.column.id,
    columns: [
      // Accessor Column
      columnHelper.accessor("firstName", {
        header: () => "firstName",
        footer: (props) => props.column.id,
      }),
      columnHelper.accessor("lastName", {
        header: () => "lastName",
        footer: (props) => props.column.id,
      }),
      columnHelper.accessor("age", {
        header: () => "Age",
        footer: (props) => props.column.id,
      }),
    ],
  }),
  columnHelper.group({
    header: "More Info",
    columns: [
      // Accessor Column
      columnHelper.accessor("visits", {
        header: () => <span>Visits</span>,
        footer: (props) => props.column.id,
      }),
      // Accessor Column
      columnHelper.accessor("status", {
        header: "Status",
        footer: (props) => props.column.id,
      }),
      // Accessor Column
      columnHelper.accessor("progress", {
        header: "Profile Progress",
        footer: (props) => props.column.id,
      }),
    ],
  }),
];
