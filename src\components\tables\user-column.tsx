"use client";

import { createColumnHelper, ColumnDef } from "@tanstack/react-table";

export type User = {
  id: number;
  email: string;
  username: string;
  gender: string;
  ip_address: string;
};

const columnHelper = createColumnHelper<User>();

export const userColumns = [
  columnHelper.accessor("id", {
    header: () => "ID",
    footer: (props) => props.column.id,
  }),
  columnHelper.accessor("email", {
    header: () => "Email",
    footer: (props) => props.column.id,
  }),
  columnHelper.accessor("username", {
    header: () => "Username",
    footer: (props) => props.column.id,
  }),
  columnHelper.accessor("gender", {
    header: () => "Gender",
    footer: (props) => props.column.id,
  }),
  columnHelper.accessor("ip_address", {
    header: () => "IP Address",
    footer: (props) => props.column.id,
  }),
] as ColumnDef<User>[];
