"use client";

import { useState } from "react";
import TableFilter from "./TableFilter";
import { VisibilityState } from "@tanstack/react-table";

interface HomeFilterProps {
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  columnVisibility?: VisibilityState;
  onColumnVisibilityChange?: (visibility: VisibilityState) => void;
}

const homeColumns = [
  { id: "firstName", label: "First Name" },
  { id: "lastName", label: "Last Name" },
  { id: "age", label: "Age" },
  { id: "visits", label: "Visits" },
  { id: "status", label: "Status" },
  { id: "progress", label: "Progress" },
];

export default function HomeFilter({
  searchValue = "",
  onSearchChange,
  columnVisibility = {},
  onColumnVisibilityChange,
}: HomeFilterProps) {
  const [internalSearchValue, setInternalSearchValue] = useState(searchValue);
  const [internalColumnVisibility, setInternalColumnVisibility] = useState<VisibilityState>(columnVisibility);

  const currentSearchValue = onSearchChange ? searchValue : internalSearchValue;
  const currentColumnVisibility = onColumnVisibilityChange ? columnVisibility : internalColumnVisibility;

  const handleSearchChange = (value: string) => {
    if (onSearchChange) {
      onSearchChange(value);
    } else {
      setInternalSearchValue(value);
    }
  };

  const handleColumnVisibilityChange = (visibility: VisibilityState) => {
    if (onColumnVisibilityChange) {
      onColumnVisibilityChange(visibility);
    } else {
      setInternalColumnVisibility(visibility);
    }
  };

  return (
    <TableFilter
      searchValue={currentSearchValue}
      onSearchChange={handleSearchChange}
      columnVisibility={currentColumnVisibility}
      onColumnVisibilityChange={handleColumnVisibilityChange}
      availableColumns={homeColumns}
    />
  );
}
