"use client";

import { Input } from "./ui/input";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function HomeFilter() {
  return (
    <div className="flex">
      <Input placeholder="Search..." className="flex-1" />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex-1">
            Open
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="flex-1 " align="start">
          <DropdownMenuGroup>
            <DropdownMenuItem>Email</DropdownMenuItem>
            <DropdownMenuItem>username</DropdownMenuItem>
            <DropdownMenuItem>gender</DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
