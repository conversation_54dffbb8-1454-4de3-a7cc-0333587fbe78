"use client";

import { useState } from "react";
import HomeFilter from "@/components/HomeFilter";
import DataTable from "@/components/tables/data-table";
import { homeColumns } from "@/components/tables/home-column";
import { VisibilityState } from "@tanstack/react-table";

const data = [
  {
    firstName: "<PERSON>",
    lastName: "<PERSON>sley",
    age: 33,
    visits: 100,
    progress: 50,
    status: "Married",
  },
  {
    firstName: "<PERSON>",
    lastName: "Vandy",
    age: 27,
    visits: 200,
    progress: 100,
    status: "Single",
  },
];

export default function Home() {
  const [searchValue, setSearchValue] = useState("");
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  return (
    <div className="">
      <HomeFilter
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={setColumnVisibility}
      />
      <DataTable
        columns={homeColumns}
        data={data}
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={setColumnVisibility}
      />
    </div>
  );
}
