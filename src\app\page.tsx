import HomeFilter from "@/components/HomeFilter";
import DataTable from "@/components/tables/data-table";
import { homeColumns } from "@/components/tables/home-column";

const data = [
  {
    firstName: "<PERSON>",
    lastName: "<PERSON><PERSON>",
    age: 33,
    visits: 100,
    progress: 50,
    status: "Married",
  },
  {
    firstName: "<PERSON>",
    lastName: "Van<PERSON>",
    age: 27,
    visits: 200,
    progress: 100,
    status: "Single",
  },
];

export default async function Home() {
  return (
    <div className="">
      <HomeFilter />
      <DataTable columns={homeColumns} data={data} />
    </div>
  );
}
